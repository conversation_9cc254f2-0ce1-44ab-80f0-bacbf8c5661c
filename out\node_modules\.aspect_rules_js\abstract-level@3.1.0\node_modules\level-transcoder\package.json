{"name": "level-transcoder", "version": "1.0.1", "description": "Encode data with built-in or custom encodings", "license": "MIT", "main": "index.js", "types": "./index.d.ts", "scripts": {"test": "ts-standard && tsc && standard && hallmark && nyc tape test/*.js", "test-browsers-local": "airtap --coverage --verbose test/*.js", "coverage": "nyc report -r lcovonly", "hallmark": "hallmark --fix"}, "files": ["lib", "index.js", "index.d.ts", "CHANGELOG.md", "LICENSE", "UPGRADING.md"], "dependencies": {"buffer": "^6.0.3", "module-error": "^1.0.1"}, "devDependencies": {"@types/node": "^16.11.10", "@voxpelli/tsconfig": "^3.1.0", "airtap": "^4.0.3", "airtap-playwright": "^1.0.1", "hallmark": "^4.0.0", "nyc": "^15.1.0", "standard": "^16.0.3", "tape": "^5.3.2", "ts-standard": "^11.0.0", "typescript": "^4.5.2"}, "repository": "Level/transcoder", "homepage": "https://github.com/Level/transcoder", "keywords": ["level"], "engines": {"node": ">=12"}}