{"telemetry.machineId": "acc61b3114683a2e5b65b72ddc07f07559cd98c9a98577db8ea245d468398ae0", "telemetry.sqmId": "{8639193A-4534-47E7-A097-5B7B8A1C7F71}", "telemetry.devDeviceId": "bfed39c9-8c70-4c12-98c3-0f9941c70731", "backupWorkspaces": {"workspaces": [], "folders": [{"folderUri": "file:///c%3A/Users/<USER>/Desktop/%E8%AE%BA%E6%96%87%E7%94%B3%E8%AF%B7"}], "emptyWindows": []}, "windowControlHeight": 35, "profileAssociations": {"workspaces": {"file:///c%3A/Users/<USER>/Desktop/newevt": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E7%A4%BE%E5%8C%BA": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E7%A4%BE%E5%8C%BA/community": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E9%B1%BC%E7%9C%BC%E9%95%9C%E5%A4%B4api/orin-fish620%E6%9C%80%E6%96%B0": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E9%B1%BC%E7%9C%BC%E9%95%9C%E5%A4%B4api": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E8%BD%A6%E8%BE%86%E7%8A%B6%E6%80%81%E6%9F%A5%E8%AF%A2%E6%9D%82%E4%BA%A4%E7%89%88": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E7%8F%AD%E7%BA%A7%E4%BA%8B%E6%83%85/%E9%A2%98%E5%BA%93/%E9%A2%98%E5%BA%93": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E8%BD%A6%E8%BE%86%E7%8A%B6%E6%80%81%E6%9F%A5%E8%AF%A2%E7%B3%BB%E7%BB%9F/621%E6%9B%B4%E6%94%B9%EF%BC%8C%E5%8F%96%E6%B6%88%E8%BE%93%E5%85%A5ip": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E6%BF%80%E5%85%89%E9%9B%B7%E8%BE%BE%E8%87%AA%E5%8A%A8%E6%A0%87%E5%AE%9A%E7%A8%8B%E5%BA%8F/%E6%BF%80%E5%85%89%E9%9B%B7%E8%BE%BE%E6%A0%87%E5%AE%9A%E7%A8%8B%E5%BA%8F": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E6%BF%80%E5%85%89%E9%9B%B7%E8%BE%BE%E8%87%AA%E5%8A%A8%E6%A0%87%E5%AE%9A%E7%A8%8B%E5%BA%8F/8.0": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E6%BF%80%E5%85%89%E9%9B%B7%E8%BE%BE%E8%87%AA%E5%8A%A8%E6%A0%87%E5%AE%9A%E7%A8%8B%E5%BA%8F/8.0%20-%20%E5%89%AF%E6%9C%AC": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E9%B1%BC%E7%9C%BC%E9%95%9C%E5%A4%B4api/%E6%91%84%E5%83%8F%E5%A4%B42.0/orin-fish": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E6%BF%80%E5%85%89%E9%9B%B7%E8%BE%BE%E8%87%AA%E5%8A%A8%E6%A0%87%E5%AE%9A%E7%A8%8B%E5%BA%8F/%E7%BB%88%E6%9E%81%E4%BF%AE%E6%94%B9": "__default__profile__", "file:///d%3A/%E6%99%BA%E8%83%BD%E7%BD%91%E8%81%94%E6%B1%BD%E8%BD%A6/%E7%99%BE%E5%BA%A6Apollo%E5%8F%B0%E6%9E%B6%E5%AD%A6%E4%B9%A0/%E7%BD%91%E9%A1%B5%E7%89%88%E6%89%8B%E5%86%8C": "__default__profile__", "file:///d%3A/%E6%99%BA%E8%83%BD%E7%BD%91%E8%81%94%E6%B1%BD%E8%BD%A6/%E7%99%BE%E5%BA%A6Apollo%E5%8F%B0%E6%9E%B6%E5%AD%A6%E4%B9%A0/%E7%BD%91%E9%A1%B5/%E7%BD%91%E9%A1%B5%E7%9F%A5%E8%AF%86%E5%BA%93/%E7%BA%AF%E7%BD%91%E9%A1%B5": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E7%A4%BE%E5%8C%BA4.0": "__default__profile__", "vscode-remote://ssh-remote%2B192.168.1.102/home/<USER>": "__default__profile__", "vscode-remote://ssh-remote%2B192.168.1.102/home": "__default__profile__", "vscode-remote://ssh-remote%2B7b22686f73744e616d65223a223139322e3136382e312e313032222c2275736572223a226e7669646961227d/home/<USER>": "__default__profile__", "vscode-remote://ssh-remote%2B7b22686f73744e616d65223a223139322e3136382e312e313032222c2275736572223a226e7669646961227d/home/<USER>/%E9%83%A8%E7%BD%B2%E8%84%9A%E6%9C%AC": "__default__profile__", "file:///d%3A/%E4%B8%93%E5%88%A9/tool/main": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E9%83%A8%E7%BD%B2%E8%84%9A%E6%9C%AC": "__default__profile__", "file:///d%3A/%E4%B8%93%E5%88%A9/pyside6": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E5%B0%8F%E5%B7%A5%E5%85%B7": "__default__profile__", "vscode-remote://ssh-remote%2B192.168.1.102/home/<USER>/work": "__default__profile__", "vscode-remote://ssh-remote%2B192.168.1.102/home/<USER>/tmp": "__default__profile__", "file:///d%3A/%E4%B8%93%E5%88%A9/tool": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E5%B0%8F%E5%B7%A5%E5%85%B7/%E5%9B%BE%E7%89%87%E5%B7%A5%E5%85%B7": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E5%90%AF%E5%8A%A8%E7%A8%8B%E5%BA%8F": "__default__profile__", "file:///e%3A/game": "__default__profile__", "file:///e%3A/game1": "__default__profile__", "file:///e%3A/game2": "__default__profile__", "file:///e%3A/game3": "__default__profile__", "file:///d%3A/temp": "__default__profile__", "vscode-remote://ssh-remote%2B192.168.161.135/home/<USER>/map": "__default__profile__", "file:///e%3A/vpn/openvpn": "__default__profile__", "file:///c%3A/Users/<USER>/PycharmProjects/pythonProject2": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E4%BB%8E%E5%A4%B4%E5%86%8D%E6%9D%A5": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E8%AE%BA%E6%96%87%E7%94%B3%E8%AF%B7": "__default__profile__"}, "emptyWindows": {}}, "windowsState": {"lastActiveWindow": {"folder": "file:///c%3A/Users/<USER>/Desktop/%E8%AE%BA%E6%96%87%E7%94%B3%E8%AF%B7", "backupPath": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\Backups\\f025e47aad7a761305fa456d4c12576f", "uiState": {"mode": 1, "x": 191, "y": 136, "width": 1200, "height": 800}}, "openedWindows": []}, "theme": "vs-dark", "themeBackground": "#1f1f1f", "windowSplash": {"zoomLevel": 0, "baseTheme": "vs-dark", "colorInfo": {"foreground": "#cccccc", "background": "#1f1f1f", "editorBackground": "#1f1f1f", "titleBarBackground": "#181818", "titleBarBorder": "#2b2b2b", "activityBarBackground": "#181818", "activityBarBorder": "#2b2b2b", "sideBarBackground": "#181818", "sideBarBorder": "#2b2b2b", "statusBarBackground": "#181818", "statusBarBorder": "#2b2b2b", "statusBarNoFolderBackground": "#1f1f1f"}, "layoutInfo": {"sideBarSide": "left", "editorPartMinWidth": 220, "titleBarHeight": 35, "activityBarWidth": 48, "sideBarWidth": 344, "auxiliarySideBarWidth": 0, "statusBarHeight": 22, "windowBorder": false}}, "windowSplashWorkspaceOverride": {"layoutInfo": {"auxiliarySideBarWidth": [300, []]}}}