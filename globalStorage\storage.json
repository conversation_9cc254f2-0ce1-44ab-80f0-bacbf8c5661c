{"backupWorkspaces": {"workspaces": [], "folders": [{"folderUri": "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BE%E7%89%87%E8%AF%86%E5%88%AB"}, {"folderUri": "file:///c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.524.1"}], "emptyWindows": [{"backupFolder": "1754826313204"}]}, "profileAssociations": {"workspaces": {"file:///c%3A/Users/<USER>/Desktop": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E4%BB%8E%E5%A4%B4%E5%86%8D%E6%9D%A5": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E5%90%AF%E5%8A%A8%E7%A8%8B%E5%BA%8F": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E5%B0%8F%E5%B7%A5%E5%85%B7": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E5%B0%8F%E5%B7%A5%E5%85%B7/%E5%9B%BE%E7%89%87%E5%B7%A5%E5%85%B7": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E6%BF%80%E5%85%89%E9%9B%B7%E8%BE%BE%E8%87%AA%E5%8A%A8%E6%A0%87%E5%AE%9A%E7%A8%8B%E5%BA%8F/%E6%BF%80%E5%85%89%E9%9B%B7%E8%BE%BE%E6%A0%87%E5%AE%9A%E7%A8%8B%E5%BA%8F": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E6%BF%80%E5%85%89%E9%9B%B7%E8%BE%BE%E8%87%AA%E5%8A%A8%E6%A0%87%E5%AE%9A%E7%A8%8B%E5%BA%8F/%E7%BB%88%E6%9E%81%E4%BF%AE%E6%94%B9": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E6%BF%80%E5%85%89%E9%9B%B7%E8%BE%BE%E8%87%AA%E5%8A%A8%E6%A0%87%E5%AE%9A%E7%A8%8B%E5%BA%8F/8.0": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E6%BF%80%E5%85%89%E9%9B%B7%E8%BE%BE%E8%87%AA%E5%8A%A8%E6%A0%87%E5%AE%9A%E7%A8%8B%E5%BA%8F/8.0%20-%20%E5%89%AF%E6%9C%AC": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E7%A4%BE%E5%8C%BA4.0": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E8%BD%A6%E8%BE%86%E7%8A%B6%E6%80%81%E6%9F%A5%E8%AF%A2%E7%B3%BB%E7%BB%9F/621%E6%9B%B4%E6%94%B9%EF%BC%8C%E5%8F%96%E6%B6%88%E8%BE%93%E5%85%A5ip": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E9%83%A8%E7%BD%B2%E8%84%9A%E6%9C%AC": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BD%E8%B5%9B%E6%96%87%E4%BB%B6/%E9%B1%BC%E7%9C%BC%E9%95%9C%E5%A4%B4api/%E6%91%84%E5%83%8F%E5%A4%B42.0/orin-fish": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E7%8F%AD%E7%BA%A7%E4%BA%8B%E6%83%85/%E9%A2%98%E5%BA%93/%E9%A2%98%E5%BA%93": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E7%A4%BE%E5%8C%BA": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E7%A4%BE%E5%8C%BA/community": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E8%AE%BA%E6%96%87%E7%94%B3%E8%AF%B7": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E8%BD%A6%E8%BE%86%E7%8A%B6%E6%80%81%E6%9F%A5%E8%AF%A2%E6%9D%82%E4%BA%A4%E7%89%88": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E9%B1%BC%E7%9C%BC%E9%95%9C%E5%A4%B4api": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E9%B1%BC%E7%9C%BC%E9%95%9C%E5%A4%B4api/orin-fish620%E6%9C%80%E6%96%B0": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/newevt": "__default__profile__", "file:///c%3A/Users/<USER>/PycharmProjects/pythonProject2": "__default__profile__", "file:///d%3A/%E4%B8%93%E5%88%A9/pyside6": "__default__profile__", "file:///d%3A/%E4%B8%93%E5%88%A9/tool": "__default__profile__", "file:///d%3A/%E4%B8%93%E5%88%A9/tool/main": "__default__profile__", "file:///d%3A/%E6%99%BA%E8%83%BD%E7%BD%91%E8%81%94%E6%B1%BD%E8%BD%A6/%E7%99%BE%E5%BA%A6Apollo%E5%8F%B0%E6%9E%B6%E5%AD%A6%E4%B9%A0/%E7%BD%91%E9%A1%B5%E7%89%88%E6%89%8B%E5%86%8C": "__default__profile__", "file:///d%3A/%E6%99%BA%E8%83%BD%E7%BD%91%E8%81%94%E6%B1%BD%E8%BD%A6/%E7%99%BE%E5%BA%A6Apollo%E5%8F%B0%E6%9E%B6%E5%AD%A6%E4%B9%A0/%E7%BD%91%E9%A1%B5/%E7%BD%91%E9%A1%B5%E7%9F%A5%E8%AF%86%E5%BA%93/%E7%BA%AF%E7%BD%91%E9%A1%B5": "__default__profile__", "file:///d%3A/temp": "__default__profile__", "file:///e%3A/game": "__default__profile__", "file:///e%3A/game1": "__default__profile__", "file:///e%3A/game2": "__default__profile__", "file:///e%3A/game3": "__default__profile__", "file:///e%3A/vpn/openvpn": "__default__profile__", "vscode-remote://ssh-remote%2B192.168.1.102/home": "__default__profile__", "vscode-remote://ssh-remote%2B192.168.1.102/home/<USER>": "__default__profile__", "vscode-remote://ssh-remote%2B192.168.1.102/home/<USER>/tmp": "__default__profile__", "vscode-remote://ssh-remote%2B192.168.1.102/home/<USER>/work": "__default__profile__", "vscode-remote://ssh-remote%2B192.168.161.135/home/<USER>/map": "__default__profile__", "vscode-remote://ssh-remote%2B7b22686f73744e616d65223a223139322e3136382e312e313032222c2275736572223a226e7669646961227d/home/<USER>": "__default__profile__", "vscode-remote://ssh-remote%2B7b22686f73744e616d65223a223139322e3136382e312e313032222c2275736572223a226e7669646961227d/home/<USER>/%E9%83%A8%E7%BD%B2%E8%84%9A%E6%9C%AC": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%AE%89%E5%8D%93%E5%B7%A5%E5%8D%95": "__default__profile__", "file:///e%3A/AndroidStudioProjects/operator": "__default__profile__", "file:///d%3A/%E4%B8%93%E5%88%A9/tool/main%E5%BE%85%E8%B0%83%E6%95%B4%E2%80%98": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/OTA%E7%A0%94%E7%A9%B6": "__default__profile__", "file:///d%3A/%E7%94%B5%E8%84%91%E5%B0%8F%E5%B7%A5%E5%85%B7/xft/Website": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E9%80%86%E5%90%91/%E6%B8%B8%E6%88%8F%E4%B8%BB%E7%95%8C%E9%9D%A2_files": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E9%80%86%E5%90%91/%E6%96%B0%E9%A1%B9%E7%9B%AE": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E9%80%86%E5%90%91/%E6%96%B0%E9%A1%B9%E7%9B%AE/%E8%BF%9B%E4%B8%80%E6%AD%A5%E7%A0%94%E7%A9%B6": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E9%80%86%E5%90%91/%E7%A0%94%E7%A9%B6": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%A4%9A%E7%B1%BB%E5%9E%8B%E6%BF%80%E5%85%89%E9%9B%B7%E8%BE%BE%E7%82%B9%E4%BA%91%E6%9F%A5%E7%9C%8B%E5%B7%A5%E5%85%B7": "__default__profile__", "vscode-remote://ssh-remote%2B192.168.2.96/home/<USER>/EX": "__default__profile__", "file:///c%3A/Users/<USER>": "__default__profile__", "file:///e%3A/AndroidStudioProjects/321pan": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E4%B8%B4%E6%97%B6%E7%A0%94%E7%A9%B6/2/main": "__default__profile__", "file:///c%3A/Users/<USER>/Desktop/%E5%9B%BE%E7%89%87%E8%AF%86%E5%88%AB": "__default__profile__", "file:///c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.524.1": "__default__profile__"}, "emptyWindows": {"1754826313204": "__default__profile__"}}, "telemetry.devDeviceId": "bfed39c9-8c70-4c12-98c3-0f9941c70731", "telemetry.machineId": "b89db470f6d566956d27baf113636a51a3d0868ba82c210c9ab45a917fa2e2fb", "telemetry.sqmId": "{8639193A-4534-47E7-A097-5B7B8A1C7F71}", "theme": "vs-dark", "themeBackground": "#1f1f1f", "windowControlHeight": 35, "windowSplash": {"zoomLevel": 0, "baseTheme": "vs-dark", "colorInfo": {"foreground": "#cccccc", "background": "#1f1f1f", "editorBackground": "#1f1f1f", "titleBarBackground": "#181818", "titleBarBorder": "#2b2b2b", "activityBarBackground": "#181818", "activityBarBorder": "#2b2b2b", "sideBarBackground": "#181818", "sideBarBorder": "#2b2b2b", "statusBarBackground": "#181818", "statusBarBorder": "#2b2b2b", "statusBarNoFolderBackground": "#1f1f1f"}, "layoutInfo": {"sideBarSide": "left", "editorPartMinWidth": 220, "titleBarHeight": 35, "activityBarWidth": 48, "sideBarWidth": 639, "auxiliarySideBarWidth": 0, "statusBarHeight": 22, "windowBorder": false}}, "windowSplashWorkspaceOverride": {"layoutInfo": {"auxiliarySideBarWidth": [300, []]}}, "windowsState": {"lastActiveWindow": {"folder": "file:///c%3A/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.524.1", "backupPath": "C:\\Users\\<USER>\\AppData\\Roaming\\Code\\Backups\\ee4a731e83ae87bc5f1673576523c00d", "uiState": {"mode": 0, "x": 113, "y": 100, "width": 1200, "height": 800}}, "openedWindows": []}}