# Augment 账号管理器使用指南

## 整合完成说明

已成功将三个文件整合为一个完整的程序：

### 整合的文件
1. **main.py** - 基础账号管理和GUI界面
2. **list.py** - 自动化登录流程（第一次更新版本）
3. **test.py** - 直接数据库操作（第二次更新版本）

### 最终文件
- `augment_manager_final.py` - 完整整合版本

## 功能特性

### 🎯 直接切换功能（推荐）
- **无需用户交互**：完全自动化的账号切换
- **直接操作数据库**：修改 VSCode 的 state.vscdb 文件
- **自动备份恢复**：操作前自动备份，失败时自动恢复
- **跨平台支持**：Windows、macOS、Linux
- **加密存储**：支持 Windows DPAPI 和 AES 加密

### 🔄 自动切换功能
- **浏览器自动化**：自动打开登录页面
- **表单自动填写**：自动输入邮箱和密码
- **OCR识别授权码**：自动识别并输入授权码
- **VSCode集成**：自动在 VSCode 中完成认证

### 📊 账号管理功能
- **账号信息展示**：计划类型、额度使用、到期时间
- **批量操作**：添加、删除、刷新多个账号
- **数据导入导出**：JSON 格式的备份和恢复
- **自动刷新**：定时更新账号信息

## 使用流程

### 1. 环境准备
```bash
# 基础依赖（必需）
pip install tkinter requests pillow pyperclip

# 自动切换功能（可选）
pip install pyautogui pynput pytesseract opencv-python numpy beautifulsoup4

# 加密功能（可选）
pip install cryptography pycryptodome

# Windows专用（可选）
pip install win32crypt psutil
```

### 2. 启动程序
```bash
python augment_manager_final.py
```

### 3. 添加账号
1. 获取 Augment Session：
   - 登录 https://app.augmentcode.com
   - 打开浏览器开发者工具 (F12)
   - 找到 Application → Cookies → `_session`
   - 复制完整的 Session 值

2. 在程序中添加：
   - 点击"添加账号"
   - 粘贴 Session 值
   - 添加备注（可选）
   - 点击"添加"

### 4. 切换账号
#### 方式一：直接切换（推荐）
1. 选择要切换的账号
2. 点击"直接切换"
3. 确认关闭 VSCode（如果正在运行）
4. 等待程序自动完成切换和重启

#### 方式二：自动切换
1. 选择要切换的账号
2. 点击"自动切换"
3. 输入账号密码（首次使用）
4. 等待自动化流程完成

## 技术实现细节

### 直接切换原理
```
1. 检测 VSCode 是否运行 → 提示关闭
2. 定位 state.vscdb 文件 → 通常在用户配置目录
3. 备份原始数据库 → 创建时间戳备份
4. 连接 SQLite 数据库 → 读取 ItemTable
5. 加密新 Session → Windows DPAPI 或 AES
6. 更新数据库记录 → 会话和认证状态
7. 重启 VSCode → 应用新会话
```

### 数据库操作
```sql
-- 会话数据键
'secret://{"extensionId":"augment.vscode-augment","key":"augment.sessions"}'

-- 认证状态键  
"Augment.vscode-augment"

-- 操作类型
UPDATE ItemTable SET value = ? WHERE key = ?
INSERT INTO ItemTable (key, value) VALUES (?, ?)
```

### 加密方案
- **Windows**: DPAPI (Data Protection API)
- **macOS/Linux**: AES-256-CBC
- **降级方案**: Base64 编码（不安全）

## 故障排除

### 常见问题

#### 1. 找不到 VSCode 数据库
**症状**: 提示"state.vscdb 文件未找到"
**解决方案**:
- 确保 VSCode 已安装并运行过
- 检查路径：
  - Windows: `%APPDATA%\Code\User\globalStorage\`
  - macOS: `~/Library/Application Support/Code/User/globalStorage/`
  - Linux: `~/.config/Code/User/globalStorage/`

#### 2. 切换后未生效
**症状**: VSCode 重启后仍是原账号
**解决方案**:
- 检查 Session 是否有效
- 查看日志文件 `augment_manager.log`
- 手动重启 VSCode
- 检查 Augment 插件是否正常

#### 3. 依赖库缺失
**症状**: 导入错误或功能不可用
**解决方案**:
- 基础功能只需要基础依赖
- 自动切换需要额外依赖
- 按需安装，不影响核心功能

#### 4. 权限问题
**症状**: 无法写入数据库或重启 VSCode
**解决方案**:
- 以管理员权限运行（Windows）
- 检查文件权限
- 确保 VSCode 完全关闭

### 日志分析
程序会生成详细日志 `augment_manager.log`：
```
2024-01-01 12:00:00 - INFO - 已创建数据库备份: /path/to/backup
2024-01-01 12:00:01 - INFO - 数据库更新成功
2024-01-01 12:00:02 - INFO - 账号切换完成
```

## 安全注意事项

1. **Session 安全**: Session 值相当于登录凭证，请妥善保管
2. **数据备份**: 程序会自动备份，但建议定期手动备份账号数据
3. **权限控制**: 避免在共享计算机上保存敏感信息
4. **网络安全**: 确保在安全网络环境下使用

## 高级用法

### 批量导入账号
1. 准备 JSON 格式的账号文件
2. 使用"导入账号"功能
3. 程序会自动去重并导入新账号

### 自定义配置
- 修改 `auto_refresh_interval` 调整自动刷新间隔
- 修改 `storage_file` 更改数据存储位置
- 修改日志级别控制输出详细程度

### 命令行使用
虽然主要是 GUI 程序，但可以通过修改代码实现命令行操作：
```python
# 示例：命令行添加账号
manager = AugmentAccountManager()
manager.add_account("your_session_here", "备注")
```

## 版本对比

| 功能 | main.py | list.py | test.py | 整合版 |
|------|---------|---------|---------|--------|
| GUI界面 | ✅ | ❌ | ✅ | ✅ |
| 账号管理 | ✅ | ❌ | ✅ | ✅ |
| 自动切换 | ❌ | ✅ | ❌ | ✅ |
| 直接切换 | ❌ | ❌ | ✅ | ✅ |
| 数据库操作 | ❌ | ❌ | ✅ | ✅ |
| 跨平台支持 | ✅ | 部分 | ✅ | ✅ |

## 总结

整合版本实现了所有三个原始版本的功能：
- **main.py** 的完整 GUI 和账号管理
- **list.py** 的自动化登录流程
- **test.py** 的直接数据库操作

推荐使用**直接切换**功能，因为它：
- 无需用户交互
- 更加可靠稳定
- 依赖库更少
- 执行速度更快
- 支持所有平台

这个方案完全实现了您需要的直接切换功能，无需用户交互即可完成账号切换，程序会自动处理加密、数据库更新和 VSCode 重启等所有步骤。
