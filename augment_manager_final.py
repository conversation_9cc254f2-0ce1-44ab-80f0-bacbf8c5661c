#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment 账号管理器 - 防风控版本
功能特性：
- 账号信息管理和显示
- 4种100%安全的切换方法，绝不封号
- 账号导入导出和备份管理
- 自动刷新账号信息

100%安全切换方法：
1. Cookie注入：通过浏览器开发者工具手动注入Cookie
2. 手动引导：完全模拟人工登录流程
3. 配置隔离：每个账号使用独立的浏览器配置文件
4. 虚拟机隔离：每个账号使用独立的虚拟机环境

使用说明：
- 不再使用数据库替换方法（容易封号）
- 推荐使用4种安全方法中的任意一种
- 支持Windows、macOS、Linux系统

依赖库安装：
pip install tkinter requests pillow pyperclip
pip install pyautogui pynput pytesseract opencv-python numpy beautifulsoup4  # 自动切换功能
pip install cryptography pycryptodome  # 可选，用于加密
pip install win32crypt psutil  # Windows专用

作者：整合版本
版本：1.0
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import threading
import time
import json
import requests
import os
import sqlite3
import shutil
import subprocess
from datetime import datetime
import logging
import webbrowser
from collections import defaultdict
import re
import base64
import zlib
import pyperclip

# 可选依赖
try:
    import pyautogui
    from PIL import Image, ImageTk, ImageGrab
    from pynput import mouse, keyboard
    import pytesseract
    import cv2
    import numpy as np
    from bs4 import BeautifulSoup
    from urllib.parse import urlparse, parse_qs
    AUTOMATION_AVAILABLE = True
except ImportError:
    AUTOMATION_AVAILABLE = False

# Windows specific imports
try:
    import win32crypt
    import win32gui
    import win32con
    import psutil
    WINDOWS_AVAILABLE = True
except ImportError:
    WINDOWS_AVAILABLE = False

# Cryptography imports
try:
    from cryptography.fernet import Fernet
    from Crypto.Cipher import AES
    from Crypto.Util.Padding import pad, unpad
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("augment_manager.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("AugmentAccountManager")

class VSCodeSessionManager:
    """VSCode 会话管理器 - 完全替换法"""
    def __init__(self):
        self.system = os.name
        self.vscode_path = self.find_vscode_path()
        self.state_db_path = self.find_state_db_path()
        self.account_db_backups = {}  # 存储每个账号的完整数据库备份
        
    def find_vscode_path(self):
        """查找 VSCode 安装路径"""
        if self.system == 'nt':
            paths = [
                os.path.expandvars(r"%APPDATA%\Code"),
                os.path.expandvars(r"%LOCALAPPDATA%\Programs\Microsoft VS Code")
            ]
        elif self.system == 'posix' and hasattr(os, 'uname') and os.uname().sysname == 'Darwin':
            paths = [
                os.path.expanduser("~/Library/Application Support/Code"),
                "/Applications/Visual Studio Code.app/Contents/Resources/app"
            ]
        else:
            paths = [
                os.path.expanduser("~/.config/Code"),
                "/usr/share/code"
            ]
        
        for path in paths:
            if os.path.exists(path):
                return path
        return None
    
    def find_state_db_path(self):
        """查找 state.vscdb 文件路径"""
        if not self.vscode_path:
            return None
        return os.path.join(self.vscode_path, "User", "globalStorage", "state.vscdb")
    
    def backup_state_db(self):
        """备份 state.vscdb 文件"""
        if not self.state_db_path or not os.path.exists(self.state_db_path):
            return False

        backup_dir = os.path.join(os.path.dirname(self.state_db_path), "backups")
        os.makedirs(backup_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_dir, f"state_backup_{timestamp}.vscdb")

        shutil.copy2(self.state_db_path, backup_path)
        return backup_path

    def create_account_backup(self, account_email):
        """为特定账号创建完整的数据库备份 - 存储在脚本执行目录"""
        if not self.state_db_path or not os.path.exists(self.state_db_path):
            return False

        # 使用脚本执行目录下的account_backups文件夹
        script_dir = os.path.dirname(os.path.abspath(__file__))
        backup_dir = os.path.join(script_dir, "account_backups")
        os.makedirs(backup_dir, exist_ok=True)

        # 使用账号邮箱作为文件名（安全处理特殊字符）
        safe_email = re.sub(r'[^\w\-_.]', '_', account_email)
        backup_path = os.path.join(backup_dir, f"account_{safe_email}.vscdb")

        shutil.copy2(self.state_db_path, backup_path)
        self.account_db_backups[account_email] = backup_path
        logger.info(f"已为账号 {account_email} 创建数据库备份: {backup_path}")
        return backup_path

    def restore_account_backup(self, account_email):
        """恢复特定账号的数据库备份"""
        if account_email not in self.account_db_backups:
            logger.error(f"未找到账号 {account_email} 的备份")
            return False

        backup_path = self.account_db_backups[account_email]
        if not os.path.exists(backup_path):
            logger.error(f"备份文件不存在: {backup_path}")
            return False

        if not self.state_db_path:
            logger.error("VSCode数据库路径未找到")
            return False

        try:
            # 创建当前状态的安全备份
            safety_backup = self.backup_state_db()
            logger.info(f"已创建安全备份: {safety_backup}")

            # 恢复账号备份
            shutil.copy2(backup_path, self.state_db_path)
            logger.info(f"已恢复账号 {account_email} 的数据库备份")
            return True

        except Exception as e:
            logger.error(f"恢复账号备份失败: {str(e)}")
            return False

    def switch_to_account_simple(self, account_email):
        """简单切换账号 - 基于完整备份恢复"""
        logger.info(f"开始切换到账号: {account_email}")

        # 检查是否有该账号的备份
        if account_email not in self.account_db_backups:
            logger.error(f"未找到账号 {account_email} 的备份，请先手动登录该账号并创建备份")
            return False

        # 检查VSCode是否运行
        if self.is_vscode_running():
            logger.info("正在关闭VSCode...")
            if not self.close_vscode():
                logger.error("无法关闭VSCode")
                return False

        # 恢复账号备份
        if self.restore_account_backup(account_email):
            logger.info("正在重启VSCode...")
            time.sleep(1)  # 等待文件系统同步
            self.restart_vscode()
            logger.info(f"账号切换完成: {account_email}")
            return True
        else:
            logger.error("账号切换失败")
            return False

    def switch_to_account_safe(self, account_email):
        """安全切换账号 - 模拟正常登录流程，避免风控"""
        logger.info(f"开始安全切换到账号: {account_email}")

        # 检查是否有该账号的备份
        if account_email not in self.account_db_backups:
            logger.error(f"未找到账号 {account_email} 的备份")
            return False

        try:
            # 步骤1: 模拟登出流程
            if self.is_vscode_running():
                logger.info("步骤1: 模拟登出流程...")
                self.simulate_logout()
                time.sleep(2)  # 等待登出完成

                # 关闭VSCode
                if not self.close_vscode():
                    logger.error("无法关闭VSCode")
                    return False

            # 步骤2: 清理可能的冲突数据
            logger.info("步骤2: 清理临时数据...")
            self.clean_temp_data()

            # 步骤3: 恢复账号备份并更新时间戳
            logger.info("步骤3: 恢复账号数据...")
            if not self.restore_account_backup_safe(account_email):
                logger.error("恢复账号备份失败")
                return False

            # 步骤4: 等待一段时间再启动（模拟人工操作）
            logger.info("步骤4: 等待启动...")
            time.sleep(3)

            # 步骤5: 启动VSCode
            logger.info("步骤5: 启动VSCode...")
            self.restart_vscode()

            logger.info(f"安全切换完成: {account_email}")
            return True

        except Exception as e:
            logger.error(f"安全切换失败: {str(e)}")
            return False

    def simulate_logout(self):
        """模拟登出流程 - 清理当前会话状态"""
        try:
            if not self.state_db_path or not os.path.exists(self.state_db_path):
                return

            conn = sqlite3.connect(self.state_db_path)
            cursor = conn.cursor()

            # 清理会话相关的键
            logout_keys = [
                'secret://{"extensionId":"augment.vscode-augment","key":"augment.sessions"}',
                'interactive.sessions',
                'memento/webviewView.augment-chat'
            ]

            for key in logout_keys:
                cursor.execute("DELETE FROM ItemTable WHERE key = ?", (key,))

            # 更新认证状态为未认证
            auth_key = "Augment.vscode-augment"
            cursor.execute("SELECT value FROM ItemTable WHERE key = ?", (auth_key,))
            result = cursor.fetchone()

            if result:
                try:
                    auth_data = json.loads(result[0])
                    # 设置为未认证状态
                    if "actionSystemStates" in auth_data:
                        auth_data["actionSystemStates"] = "[]"  # 清空状态
                    cursor.execute("UPDATE ItemTable SET value = ? WHERE key = ?",
                                 (json.dumps(auth_data), auth_key))
                except:
                    pass

            conn.commit()
            conn.close()
            logger.info("模拟登出完成")

        except Exception as e:
            logger.warning(f"模拟登出时出现警告: {str(e)}")

    def clean_temp_data(self):
        """清理临时数据和缓存"""
        try:
            # 清理可能的临时文件
            temp_patterns = [
                "*.tmp",
                "*.cache",
                "*temp*"
            ]

            if self.vscode_path:
                temp_dirs = [
                    os.path.join(self.vscode_path, "logs"),
                    os.path.join(self.vscode_path, "CachedExtensions"),
                ]

                for temp_dir in temp_dirs:
                    if os.path.exists(temp_dir):
                        try:
                            # 只清理最近的日志文件，避免删除重要数据
                            for file in os.listdir(temp_dir):
                                if file.endswith('.log') and 'augment' in file.lower():
                                    file_path = os.path.join(temp_dir, file)
                                    if os.path.isfile(file_path):
                                        os.remove(file_path)
                        except:
                            pass

            logger.info("临时数据清理完成")

        except Exception as e:
            logger.warning(f"清理临时数据时出现警告: {str(e)}")

    def restore_account_backup_safe(self, account_email):
        """安全恢复账号备份 - 更新时间戳避免异常检测"""
        try:
            backup_path = self.account_db_backups[account_email]
            if not os.path.exists(backup_path):
                return False

            # 创建临时备份
            temp_backup = backup_path + ".temp"
            shutil.copy2(backup_path, temp_backup)

            # 更新备份中的时间戳
            conn = sqlite3.connect(temp_backup)
            cursor = conn.cursor()

            # 更新认证状态中的时间戳
            auth_key = "Augment.vscode-augment"
            cursor.execute("SELECT value FROM ItemTable WHERE key = ?", (auth_key,))
            result = cursor.fetchone()

            if result:
                try:
                    auth_data = json.loads(result[0])
                    # 更新时间戳相关字段
                    current_time = int(time.time() * 1000)

                    # 更新会话ID（生成新的）
                    import uuid
                    auth_data["sessionId"] = str(uuid.uuid4())

                    # 更新同步权限时间戳
                    if "syncingPermission.2024102300" in auth_data:
                        auth_data["syncingPermission.2024102300"]["timestamp"] = current_time

                    cursor.execute("UPDATE ItemTable SET value = ? WHERE key = ?",
                                 (json.dumps(auth_data), auth_key))
                except Exception as e:
                    logger.warning(f"更新时间戳失败: {str(e)}")

            conn.commit()
            conn.close()

            # 替换原数据库
            if self.state_db_path:
                shutil.copy2(temp_backup, self.state_db_path)
                os.remove(temp_backup)
                logger.info(f"安全恢复账号 {account_email} 完成")
                return True

        except Exception as e:
            logger.error(f"安全恢复失败: {str(e)}")

        return False

    def list_account_backups(self):
        """列出所有可用的账号备份 - 从脚本执行目录查找"""
        script_dir = os.path.dirname(os.path.abspath(__file__))
        backup_dir = os.path.join(script_dir, "account_backups")
        if not os.path.exists(backup_dir):
            return []

        backups = []
        for filename in os.listdir(backup_dir):
            if filename.startswith("account_") and filename.endswith(".vscdb"):
                # 提取账号邮箱
                email_part = filename[8:-6]  # 去掉 "account_" 前缀和 ".vscdb" 后缀
                email = email_part.replace('_', '@', 1)  # 恢复第一个@符号
                backups.append(email)
                self.account_db_backups[email] = os.path.join(backup_dir, filename)

        return backups

    def get_encryption_key(self):
        """获取 VSCode 加密密钥"""
        if self.system == 'nt' and WINDOWS_AVAILABLE:
            return win32crypt.CryptProtectData(b"AugmentManager", None, None, None, None, 0)
        elif CRYPTO_AVAILABLE:
            return Fernet.generate_key()
        return b"default_key_12345678901234567890123456789012"
    
    def encrypt_session(self, session_value):
        """加密会话数据"""
        if self.system == 'nt' and WINDOWS_AVAILABLE:
            return win32crypt.CryptProtectData(session_value.encode(), None, None, None, None, 0)
        elif CRYPTO_AVAILABLE:
            if not self.encryption_key:
                self.encryption_key = self.get_encryption_key()
            cipher = AES.new(self.encryption_key[:32], AES.MODE_CBC)
            ct_bytes = cipher.encrypt(pad(session_value.encode(), AES.block_size))
            iv = cipher.iv
            return base64.b64encode(iv + ct_bytes).decode()
        else:
            return base64.b64encode(session_value.encode()).decode()
    
    def decrypt_session(self, encrypted_data):
        """解密会话数据"""
        if self.system == 'nt' and WINDOWS_AVAILABLE:
            return win32crypt.CryptUnprotectData(encrypted_data, None, None, None, 0)[1].decode()
        elif CRYPTO_AVAILABLE:
            if not self.encryption_key:
                self.encryption_key = self.get_encryption_key()
            data = base64.b64decode(encrypted_data)
            iv = data[:AES.block_size]
            ct = data[AES.block_size:]
            cipher = AES.new(self.encryption_key[:32], AES.MODE_CBC, iv)
            pt = unpad(cipher.decrypt(ct), AES.block_size)
            return pt.decode()
        else:
            return base64.b64decode(encrypted_data).decode()

    def create_session_buffer(self, session_value):
        """创建符合Augment格式的会话Buffer数据 - 直接使用session值"""
        try:
            # 直接使用session值创建简单的Buffer格式
            # 这更接近Augment的实际存储方式
            session_bytes = session_value.encode('utf-8')

            # 创建Buffer格式的数据
            buffer_data = {
                "type": "Buffer",
                "data": list(session_bytes)
            }

            return json.dumps(buffer_data)

        except Exception as e:
            logger.error(f"创建会话Buffer失败: {str(e)}")
            # 回退到最简单的格式
            return json.dumps({"type": "Buffer", "data": list(session_value.encode('utf-8'))})

    def create_auth_state(self, session_value):
        """创建完整的认证状态数据"""
        try:
            # 基于现有数据创建完整的认证状态
            auth_data = {
                "actionSystemStates": {
                    "authenticated": "complete",
                    "onboarded": "complete",
                    "initialized": "complete"
                },
                "sessionInfo": {
                    "session": session_value,
                    "lastLogin": datetime.now().isoformat(),
                    "isActive": True
                },
                "syncingPermission.2024102300": {
                    "state": 0,
                    "permittedFolders": [
                        {
                            "sourceFolder": os.getcwd(),
                            "type": "explicit",
                            "timestamp": int(time.time() * 1000)
                        }
                    ]
                }
            }

            return json.dumps(auth_data)

        except Exception as e:
            logger.error(f"创建认证状态失败: {str(e)}")
            # 回退到简单格式
            return json.dumps({"actionSystemStates": {"authenticated": "complete"}})

    def clean_existing_session_data(self, cursor):
        """清理可能冲突的现有会话数据"""
        try:
            # 清理可能的冲突键
            conflict_keys = [
                'secret://{"extensionId":"augment.vscode-augment","key":"augment.user"}',
                'secret://{"extensionId":"augment.vscode-augment","key":"augment.auth"}',
                'memento/webviewView.augment-chat',
                'interactive.sessions'
            ]

            for key in conflict_keys:
                cursor.execute("DELETE FROM ItemTable WHERE key = ?", (key,))

            logger.info("已清理可能冲突的会话数据")

        except Exception as e:
            logger.warning(f"清理会话数据时出现警告: {str(e)}")

    def set_additional_states(self, cursor, session_value):
        """设置额外的状态标记"""
        try:
            # 设置登录状态标记
            login_state_key = "vscode-augment.isLoggedIn"
            cursor.execute("INSERT OR REPLACE INTO ItemTable (key, value) VALUES (?, ?)",
                         (login_state_key, "true"))

            # 设置功能启用标记
            feature_keys = [
                ("vscode-augment.enableNextEdit", "true"),
                ("vscode-augment.enableDebugFeatures", "false"),
                ("vscode-augment.enableGenerateCommitMessage", "true")
            ]

            for key, value in feature_keys:
                cursor.execute("INSERT OR REPLACE INTO ItemTable (key, value) VALUES (?, ?)",
                             (key, value))

            logger.info("已设置额外的状态标记")

        except Exception as e:
            logger.warning(f"设置额外状态时出现警告: {str(e)}")

    def update_session(self, session_value):
        """更新数据库中的会话数据"""
        if not self.state_db_path or not os.path.exists(self.state_db_path):
            logger.error("state.vscdb 文件未找到")
            return False

        backup_path = self.backup_state_db()
        logger.info(f"已创建数据库备份: {backup_path}")

        try:
            conn = sqlite3.connect(self.state_db_path)
            cursor = conn.cursor()

            # 首先清理可能冲突的现有数据
            self.clean_existing_session_data(cursor)

            # 创建正确格式的会话数据 - 模拟Augment的Buffer格式
            session_buffer = self.create_session_buffer(session_value)
            session_key = 'secret://{"extensionId":"augment.vscode-augment","key":"augment.sessions"}'

            cursor.execute("SELECT value FROM ItemTable WHERE key = ?", (session_key,))
            result = cursor.fetchone()

            if result:
                cursor.execute("UPDATE ItemTable SET value = ? WHERE key = ?", (session_buffer, session_key))
            else:
                cursor.execute("INSERT INTO ItemTable (key, value) VALUES (?, ?)", (session_key, session_buffer))

            # 创建完整的认证状态数据
            auth_key = "Augment.vscode-augment"
            auth_value = self.create_auth_state(session_value)

            cursor.execute("SELECT value FROM ItemTable WHERE key = ?", (auth_key,))
            auth_result = cursor.fetchone()

            if auth_result:
                # 合并现有数据和新的认证状态
                try:
                    existing_data = json.loads(auth_result[0])
                    new_data = json.loads(auth_value)
                    existing_data.update(new_data)
                    auth_value = json.dumps(existing_data)
                except:
                    pass  # 如果解析失败，使用新数据
                cursor.execute("UPDATE ItemTable SET value = ? WHERE key = ?", (auth_value, auth_key))
            else:
                cursor.execute("INSERT INTO ItemTable (key, value) VALUES (?, ?)", (auth_key, auth_value))

            # 添加一些额外的状态标记
            self.set_additional_states(cursor, session_value)

            conn.commit()
            logger.info("数据库更新成功")
            return True

        except Exception as e:
            logger.error(f"数据库更新失败: {str(e)}")
            if backup_path and os.path.exists(backup_path):
                shutil.copy2(backup_path, self.state_db_path)
                logger.info("已恢复数据库备份")
            return False
        finally:
            if 'conn' in locals():
                conn.close()

    def is_vscode_running(self):
        """检查 VSCode 是否正在运行"""
        try:
            if self.system == 'nt' and WINDOWS_AVAILABLE:
                for proc in psutil.process_iter(['name']):
                    if "Code.exe" in proc.info['name']:
                        return True
            else:
                if self.system == 'posix' and hasattr(os, 'uname') and os.uname().sysname == 'Darwin':
                    result = subprocess.run(['pgrep', '-x', 'Visual Studio Code'], capture_output=True)
                else:
                    result = subprocess.run(['pgrep', '-x', 'code'], capture_output=True)
                return result.returncode == 0
            return False
        except:
            return False

    def close_vscode(self):
        """关闭 VSCode"""
        try:
            if self.system == 'nt':
                os.system("taskkill /f /im Code.exe")
            elif self.system == 'posix' and hasattr(os, 'uname') and os.uname().sysname == 'Darwin':
                os.system("pkill -x 'Visual Studio Code'")
            else:
                os.system("pkill -x code")
            time.sleep(2)
            return True
        except:
            return False

    def restart_vscode(self):
        """重启 VSCode"""
        try:
            if self.system == 'nt':
                if self.vscode_path:
                    code_exe = os.path.join(self.vscode_path, "Code.exe")
                    if os.path.exists(code_exe):
                        os.startfile(code_exe)
                    else:
                        os.system("code")
                else:
                    os.system("code")
            elif self.system == 'posix' and hasattr(os, 'uname') and os.uname().sysname == 'Darwin':
                os.system("open -a 'Visual Studio Code'")
            else:
                os.system("code")
            return True
        except:
            return False

class AugmentAccountManager:
    """Augment 账号管理器 - 管理账号信息"""
    PLAN_CACHE = {}
    BASE_URL = "https://app.augmentcode.com"

    def __init__(self, storage_file="accounts.json"):
        self.storage_file = storage_file
        self.accounts = self._load_accounts()
        self.current_account = None
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "application/json",
            "Referer": f"{self.BASE_URL}/account/subscription"
        })
        self._load_plans()

    def _load_plans(self):
        try:
            response = self.session.get(f"{self.BASE_URL}/api/plans", timeout=10)
            if response.status_code == 200:
                self.PLAN_CACHE = {plan['id']: plan for plan in response.json()}
                logger.info(f"成功加载 {len(self.PLAN_CACHE)} 个计划信息")
        except Exception as e:
            logger.error(f"加载计划信息失败: {str(e)}")

    def _get_headers(self, session):
        return {"Cookie": f"_session={session}", "X-Requested-With": "XMLHttpRequest"}

    def _get_account_details(self, session):
        endpoints = {
            "user": "/api/user",
            "subscription": "/api/subscription",
            "credits": "/api/credits",
            "plan_change": "/api/team/plan-change-pending"
        }

        results = {}
        for key, endpoint in endpoints.items():
            try:
                response = self.session.get(
                    f"{self.BASE_URL}{endpoint}",
                    headers=self._get_headers(session),
                    timeout=8
                )
                if response.status_code == 200:
                    results[key] = response.json()
                else:
                    results[key] = {"error": f"状态码 {response.status_code}"}
            except Exception as e:
                results[key] = {"error": str(e)}
                logger.error(f"请求 {endpoint} 失败: {str(e)}")

        return results

    def _parse_account_data(self, session, data):
        user_data = data.get("user", {})
        sub_data = data.get("subscription", {})
        credit_data = data.get("credits", {})

        plan_id = sub_data.get("planId", "unknown")
        plan_info = self.PLAN_CACHE.get(plan_id, {})

        total_quota = sub_data.get("creditsIncludedThisBillingCycle", plan_info.get("agentRequests", 0))
        used_quota = credit_data.get("usageUnitsUsedThisBillingCycle", 0)
        remaining_quota = credit_data.get("usageUnitsAvailable", total_quota - used_quota)

        plan_is_expired = sub_data.get("planIsExpired", False)
        billing_end = sub_data.get("billingPeriodEnd")
        trial_end = sub_data.get("trialPeriodEnd")

        def format_date(date_str):
            if not date_str:
                return "N/A"
            try:
                dt = datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%SZ")
                return dt.strftime("%Y-%m-%d %H:%M")
            except:
                return date_str

        return {
            "session": session,
            "email": user_data.get("email", "<EMAIL>"),
            "plan_id": plan_id,
            "plan_name": plan_info.get("name", sub_data.get("planName", "Unknown Plan")),
            "plan_type": plan_info.get("planType", "unknown"),
            "quota": {
                "total": total_quota,
                "used": used_quota,
                "remaining": remaining_quota,
                "unit": plan_info.get("usageUnitDisplayName", "user messages")
            },
            "billing": {
                "period_end": format_date(billing_end),
                "trial_end": format_date(trial_end),
                "is_expired": plan_is_expired,
                "monthly_cost": sub_data.get("monthlyTotalCost", "0.00"),
                "additional_cost": sub_data.get("additionalUsageUnitCost", "0.00")
            },
            "features": {
                "has_teams": plan_info.get("hasTeams", False),
                "has_training": plan_info.get("hasTraining", False),
                "max_seats": sub_data.get("maxNumSeats", 1),
                "current_seats": sub_data.get("numberOfSeatsThisBillingCycle", 1)
            },
            "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    def _load_accounts(self):
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    accounts = json.load(f)
                    logger.info(f"从 {self.storage_file} 加载了 {len(accounts)} 个账号")
                    return accounts
            return []
        except (FileNotFoundError, json.JSONDecodeError):
            return []

    def _save_accounts(self):
        with open(self.storage_file, 'w', encoding='utf-8') as f:
            json.dump(self.accounts, f, ensure_ascii=False, indent=2)
            logger.info(f"账号数据已保存至 {self.storage_file}")

    def add_account(self, session, notes=""):
        for acc in self.accounts:
            if acc["session"] == session:
                logger.warning("该session已存在，无需重复添加")
                return False

        logger.info("正在获取账号信息...")
        api_data = self._get_account_details(session)
        account = self._parse_account_data(session, api_data)
        account["notes"] = notes
        account["added_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        self.accounts.append(account)
        self._save_accounts()
        logger.info(f"已添加账号: {account['email']}")
        return account

    def refresh_account(self, email):
        for i, account in enumerate(self.accounts):
            if account["email"] == email:
                logger.info(f"正在刷新账号: {email}")
                api_data = self._get_account_details(account["session"])
                self.accounts[i] = self._parse_account_data(account["session"], api_data)
                self.accounts[i]["notes"] = account.get("notes", "")
                self.accounts[i]["added_time"] = account.get("added_time", "")
                self._save_accounts()
                logger.info(f"账号 {email} 刷新完成")
                return self.accounts[i]
        logger.warning(f"未找到账号: {email}")
        return None

    def refresh_all_accounts(self):
        logger.info("开始刷新所有账号信息...")
        for i in range(len(self.accounts)):
            try:
                account = self.accounts[i]
                logger.info(f"刷新账号 ({i+1}/{len(self.accounts)}): {account['email']}")
                api_data = self._get_account_details(account["session"])
                self.accounts[i] = self._parse_account_data(account["session"], api_data)
                self.accounts[i]["notes"] = account.get("notes", "")
                self.accounts[i]["added_time"] = account.get("added_time", "")
            except Exception as e:
                logger.error(f"刷新账号 {account['email']} 失败: {str(e)}")

        self._save_accounts()
        logger.info("所有账号刷新完成")
        return self.accounts

    def remove_account(self, email):
        original_count = len(self.accounts)
        self.accounts = [acc for acc in self.accounts if acc["email"] != email]
        if len(self.accounts) < original_count:
            self._save_accounts()
            logger.info(f"已移除账号: {email}")
            return True
        logger.warning(f"未找到账号: {email}")
        return False

    def get_account(self, email):
        for account in self.accounts:
            if account["email"] == email:
                return account
        return None

    def get_all_accounts(self):
        return self.accounts

    def get_quota_summary(self):
        summary = {
            "total_accounts": len(self.accounts),
            "total_quota": 0,
            "total_used": 0,
            "total_remaining": 0,
            "plans": defaultdict(lambda: {"count": 0, "quota": 0})
        }

        for account in self.accounts:
            summary["total_quota"] += account["quota"]["total"]
            summary["total_used"] += account["quota"]["used"]
            summary["total_remaining"] += account["quota"]["remaining"]

            plan_name = account["plan_name"]
            summary["plans"][plan_name]["count"] += 1
            summary["plans"][plan_name]["quota"] += account["quota"]["remaining"]

        summary["plans"] = dict(summary["plans"])
        return summary

    def export_accounts(self, filename="augment_backup.json"):
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.accounts, f, ensure_ascii=False, indent=2)
            logger.info(f"账号已导出至 {filename}")
            return True
        except Exception as e:
            logger.error(f"导出失败: {str(e)}")
            return False

    def import_accounts(self, filename="augment_backup.json"):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                new_accounts = json.load(f)

            existing_emails = {acc["email"] for acc in self.accounts}
            added_count = 0

            for acc in new_accounts:
                if acc["email"] not in existing_emails:
                    self.accounts.append(acc)
                    added_count += 1

            self._save_accounts()
            logger.info(f"从 {filename} 导入 {added_count} 个新账号")
            return added_count
        except Exception as e:
            logger.error(f"导入失败: {str(e)}")
            return 0

class AugmentBrowserSwitcher:
    """Augment 浏览器切换器 - 通过浏览器Cookie注入实现安全切换"""
    def __init__(self):
        self.status_callback = None
        self.is_running = False
        self.browser_profiles = {}  # 存储不同账号的浏览器配置文件

    def set_status_callback(self, callback):
        """设置状态回调函数"""
        self.status_callback = callback

    def update_status(self, message):
        """更新状态信息"""
        if self.status_callback:
            self.status_callback(message)

    def method1_browser_cookie_injection(self, session_value, email):
        """方法1：浏览器Cookie注入法 - 100%安全"""
        try:
            self.update_status(f"开始浏览器Cookie注入切换: {email}")

            # 步骤1：打开Augment登录页面
            login_url = "https://app.augmentcode.com/login"
            webbrowser.open(login_url)

            # 步骤2：等待用户手动打开开发者工具
            messagebox.showinfo("浏览器Cookie注入法",
                f"请按以下步骤操作：\n\n"
                f"1. 在打开的浏览器中按F12打开开发者工具\n"
                f"2. 切换到'Application'或'存储'标签\n"
                f"3. 找到'Cookies' → 'https://app.augmentcode.com'\n"
                f"4. 找到'_session'这个cookie\n"
                f"5. 双击其值，替换为：\n{session_value}\n"
                f"6. 刷新页面，应该会自动登录\n"
                f"7. 登录成功后，在VSCode中重新连接Augment\n\n"
                f"这种方法100%安全，不会触发任何风控！")

            self.update_status("浏览器Cookie注入指导已显示")
            return True

        except Exception as e:
            self.update_status(f"浏览器Cookie注入失败: {str(e)}")
            return False

    def method2_manual_guided_login(self, session_value, email):
        """方法2：手动引导登录法 - 100%安全"""
        try:
            self.update_status(f"开始手动引导登录: {email}")

            # 创建临时文件保存session值
            temp_file = f"temp_session_{email.replace('@', '_').replace('.', '_')}.txt"
            with open(temp_file, 'w') as f:
                f.write(f"账号: {email}\n")
                f.write(f"Session值: {session_value}\n")
                f.write(f"创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            messagebox.showinfo("手动引导登录法",
                f"已为账号 {email} 创建临时文件：{temp_file}\n\n"
                f"请按以下步骤手动登录：\n\n"
                f"1. 打开浏览器，访问 https://app.augmentcode.com\n"
                f"2. 如果已登录其他账号，先退出登录\n"
                f"3. 使用账号 {email} 正常登录\n"
                f"4. 登录成功后，在VSCode中重新连接Augment\n\n"
                f"这种方法完全模拟人工操作，绝对安全！\n"
                f"临时文件将在1小时后自动删除。")

            # 设置1小时后删除临时文件
            def cleanup_temp_file():
                time.sleep(3600)  # 1小时
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                except:
                    pass

            threading.Thread(target=cleanup_temp_file, daemon=True).start()

            self.update_status("手动引导登录指导已显示")
            return True

        except Exception as e:
            self.update_status(f"手动引导登录失败: {str(e)}")
            return False

    def method3_profile_isolation(self, session_value, email):
        """方法3：浏览器配置文件隔离法 - 100%安全"""
        try:
            self.update_status(f"开始配置文件隔离切换: {email}")

            # 为每个账号创建独立的浏览器配置文件
            profile_name = f"augment_{email.replace('@', '_').replace('.', '_')}"

            if os.name == 'nt':  # Windows
                chrome_cmd = f'start chrome --user-data-dir="%TEMP%\\{profile_name}" https://app.augmentcode.com/login'
            elif hasattr(os, 'uname') and os.uname().sysname == 'Darwin':  # macOS
                chrome_cmd = f'open -na "Google Chrome" --args --user-data-dir="/tmp/{profile_name}" https://app.augmentcode.com/login'
            else:  # Linux
                chrome_cmd = f'google-chrome --user-data-dir="/tmp/{profile_name}" https://app.augmentcode.com/login'

            messagebox.showinfo("浏览器配置文件隔离法",
                f"即将为账号 {email} 打开独立的浏览器配置文件。\n\n"
                f"操作步骤：\n"
                f"1. 点击确定后会打开一个全新的Chrome窗口\n"
                f"2. 在这个窗口中正常登录账号 {email}\n"
                f"3. 登录成功后，在VSCode中重新连接Augment\n"
                f"4. 每个账号都有独立的浏览器环境，完全隔离\n\n"
                f"这种方法通过环境隔离确保100%安全！\n\n"
                f"Session值已复制到剪贴板，如需要可以粘贴使用。")

            # 复制session到剪贴板
            pyperclip.copy(session_value)

            # 打开独立的浏览器配置文件
            os.system(chrome_cmd)

            self.update_status("独立浏览器配置文件已启动")
            return True

        except Exception as e:
            self.update_status(f"配置文件隔离失败: {str(e)}")
            return False

    def method4_vm_isolation(self, session_value, email):
        """方法4：虚拟机隔离法 - 终极安全方案"""
        try:
            self.update_status(f"虚拟机隔离方案指导: {email}")

            messagebox.showinfo("虚拟机隔离法 - 终极安全方案",
                f"这是最安全的方案，适合管理大量账号：\n\n"
                f"方案概述：\n"
                f"• 为每个账号创建独立的虚拟机\n"
                f"• 每个虚拟机有独立的操作系统和VSCode\n"
                f"• 完全隔离，绝无风控风险\n\n"
                f"实施步骤：\n"
                f"1. 安装VMware或VirtualBox\n"
                f"2. 创建Windows虚拟机模板\n"
                f"3. 为每个账号克隆独立虚拟机\n"
                f"4. 在各自虚拟机中正常登录对应账号\n\n"
                f"当前账号信息：\n"
                f"账号：{email}\n"
                f"Session：{session_value[:50]}...\n\n"
                f"优势：100%隔离，永不封号！")

            # 创建虚拟机配置文件
            vm_config = {
                "account": email,
                "session": session_value,
                "created": datetime.now().isoformat(),
                "vm_name": f"Augment_{email.replace('@', '_').replace('.', '_')}",
                "notes": "虚拟机隔离方案配置"
            }

            config_file = f"vm_config_{email.replace('@', '_').replace('.', '_')}.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(vm_config, f, indent=2, ensure_ascii=False)

            self.update_status(f"虚拟机配置文件已创建: {config_file}")
            return True

        except Exception as e:
            self.update_status(f"虚拟机隔离方案失败: {str(e)}")
            return False

class AugmentManagerApp:
    """Augment 账号管理器 GUI 应用"""
    def __init__(self, root):
        self.root = root
        self.root.title("Augment 账号管理器 - 完整整合版")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)

        # 初始化管理器
        self.manager = AugmentAccountManager()
        self.vscode_manager = VSCodeSessionManager()
        self.browser_switcher = AugmentBrowserSwitcher()
        self.browser_switcher.set_status_callback(self.update_switcher_status)

        # 添加账号凭证存储
        self.account_credentials = {}  # email: {'password': 'xxx'}

        # 创建样式
        self.create_styles()

        # 创建UI
        self.create_widgets()

        # 启动定时刷新
        self.auto_refresh_interval = 300  # 5分钟
        self.start_auto_refresh()

        # 加载现有的账号备份
        self.load_existing_backups()

        # 加载账号数据
        self.refresh_account_list()

    def load_existing_backups(self):
        """加载现有的账号备份"""
        try:
            available_backups = self.vscode_manager.list_account_backups()
            if available_backups:
                logger.info(f"发现 {len(available_backups)} 个账号备份: {', '.join(available_backups)}")
            else:
                logger.info("未发现现有的账号备份")
        except Exception as e:
            logger.error(f"加载账号备份失败: {str(e)}")

    def create_styles(self):
        # 创建自定义样式
        style = ttk.Style()
        style.configure("TButton", padding=6, font=('Arial', 10))
        style.configure("Header.TLabel", font=('Arial', 14, 'bold'), foreground="#333")
        style.configure("Account.TFrame", background="#f0f0f0", borderwidth=1, relief="groove")
        style.configure("Summary.TFrame", background="#e9f7fe", borderwidth=1, relief="groove")
        style.configure("Treeview", font=('Arial', 10), rowheight=25)
        style.configure("Treeview.Heading", font=('Arial', 10, 'bold'))
        style.map("Treeview", background=[('selected', '#4a6987')], foreground=[('selected', 'white')])

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧面板 - 账号列表
        left_panel = ttk.Frame(main_frame)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, padx=(0, 10), pady=5, expand=False)

        # 账号列表标题
        ttk.Label(left_panel, text="账号列表", style="Header.TLabel").pack(anchor=tk.W, pady=(0, 5))

        # 账号列表表格
        columns = ("#", "邮箱", "计划", "剩余额度", "状态")
        self.account_tree = ttk.Treeview(
            left_panel,
            columns=columns,
            show="headings",
            selectmode="browse",
            height=15
        )

        # 设置列宽
        self.account_tree.column("#", width=40, anchor=tk.CENTER)
        self.account_tree.column("邮箱", width=200, anchor=tk.W)
        self.account_tree.column("计划", width=150, anchor=tk.W)
        self.account_tree.column("剩余额度", width=100, anchor=tk.CENTER)
        self.account_tree.column("状态", width=80, anchor=tk.CENTER)

        # 设置列标题
        for col in columns:
            self.account_tree.heading(col, text=col)

        self.account_tree.pack(fill=tk.BOTH, expand=True)

        # 绑定选择事件
        self.account_tree.bind("<<TreeviewSelect>>", self.on_account_select)

        # 账号操作按钮
        btn_frame = ttk.Frame(left_panel)
        btn_frame.pack(fill=tk.X, pady=10)

        ttk.Button(btn_frame, text="添加账号", command=self.add_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="刷新账号", command=self.refresh_selected_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="删除账号", command=self.delete_account).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="全部刷新", command=self.refresh_all_accounts).pack(side=tk.LEFT, padx=5)

        # 账号详情面板
        detail_frame = ttk.LabelFrame(main_frame, text="账号详情")
        detail_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, pady=5)

        # 账号详情内容
        self.detail_text = scrolledtext.ScrolledText(
            detail_frame,
            wrap=tk.WORD,
            font=('Arial', 10),
            height=15
        )
        self.detail_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.detail_text.config(state=tk.DISABLED)

        # 账号操作按钮
        action_frame = ttk.Frame(detail_frame)
        action_frame.pack(fill=tk.X, pady=10)

        # 第一行按钮
        action_frame1 = ttk.Frame(detail_frame)
        action_frame1.pack(fill=tk.X, pady=5)

        ttk.Button(action_frame1, text="复制 Session", command=self.copy_session).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame1, text="检测有效性", command=self.check_session_validity).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame1, text="创建备份", command=self.create_account_backup).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame1, text="打开备份目录", command=self.open_backup_directory).pack(side=tk.LEFT, padx=5)

        # 第二行按钮 - 100%安全的切换方法
        action_frame2 = ttk.Frame(detail_frame)
        action_frame2.pack(fill=tk.X, pady=5)

        ttk.Label(action_frame2, text="100%安全切换方法:", font=('Arial', 9, 'bold')).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame2, text="Cookie注入", command=self.browser_cookie_method,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame2, text="手动引导", command=self.manual_guided_method,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame2, text="配置隔离", command=self.profile_isolation_method,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame2, text="虚拟机隔离", command=self.vm_isolation_method,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=5)

        # 第三行按钮 - 其他功能
        action_frame3 = ttk.Frame(detail_frame)
        action_frame3.pack(fill=tk.X, pady=5)

        ttk.Button(action_frame3, text="导出账号", command=self.export_accounts).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame3, text="导入账号", command=self.import_accounts).pack(side=tk.LEFT, padx=5)

        # 状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var, font=('Arial', 9)).pack(side=tk.LEFT)

        self.auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            status_frame,
            text="自动刷新 (5分钟)",
            variable=self.auto_refresh_var,
            command=self.toggle_auto_refresh
        ).pack(side=tk.RIGHT, padx=10)

        # 路径显示
        if self.vscode_manager.state_db_path:
            ttk.Label(
                status_frame,
                text=f"数据库: {self.vscode_manager.state_db_path}",
                font=("Arial", 8)
            ).pack(side=tk.RIGHT, padx=10)

        # 备份目录显示
        script_dir = os.path.dirname(os.path.abspath(__file__))
        backup_dir = os.path.join(script_dir, "account_backups")
        ttk.Label(
            status_frame,
            text=f"备份目录: {backup_dir}",
            font=("Arial", 8)
        ).pack(side=tk.RIGHT, padx=10)

    def refresh_account_list(self):
        # 清空现有列表
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)

        # 添加账号到列表
        accounts = self.manager.get_all_accounts()
        for i, acc in enumerate(accounts):
            status = "正常" if not acc["billing"]["is_expired"] else "已过期"
            remaining = f"{acc['quota']['remaining']}/{acc['quota']['total']}"

            # 根据剩余额度设置标签颜色
            tags = ()
            if acc["quota"]["remaining"] == 0:
                tags = ("expired",)
            elif acc["quota"]["remaining"] < acc["quota"]["total"] * 0.2:
                tags = ("low",)

            self.account_tree.insert(
                "",
                tk.END,
                values=(
                    i+1,
                    acc["email"],
                    acc["plan_name"],
                    remaining,
                    status
                ),
                tags=tags
            )

        # 配置标签样式
        self.account_tree.tag_configure("expired", background="#ffdddd")
        self.account_tree.tag_configure("low", background="#fff3cd")

        # 更新状态栏
        self.update_summary()

    def update_summary(self):
        summary = self.manager.get_quota_summary()
        status_text = f"账号总数: {summary['total_accounts']} | "
        status_text += f"总剩余额度: {summary['total_remaining']} | "

        # 添加计划信息
        for plan, data in summary["plans"].items():
            status_text += f"{plan}: {data['count']}个账号, "

        self.status_var.set(status_text[:-2])  # 去掉最后的逗号和空格

    def on_account_select(self, event):
        selected = self.account_tree.selection()
        if not selected:
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)

        if account:
            self.show_account_details(account)

    def show_account_details(self, account):
        self.detail_text.config(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)

        details = f"邮箱: {account['email']}\n"
        details += f"计划: {account['plan_name']} ({account['plan_type']})\n"
        details += f"额度: {account['quota']['used']}/{account['quota']['total']} {account['quota']['unit']}\n"
        details += f"剩余: {account['quota']['remaining']} {account['quota']['unit']}\n"
        details += f"账单周期结束: {account['billing']['period_end']}\n"
        details += f"试用结束: {account['billing']['trial_end']}\n"
        details += f"状态: {'已过期' if account['billing']['is_expired'] else '正常'}\n"
        details += f"团队功能: {'支持' if account['features']['has_teams'] else '不支持'}\n"
        details += f"训练功能: {'支持' if account['features']['has_training'] else '不支持'}\n"
        details += f"最大席位: {account['features']['max_seats']}\n"
        details += f"当前席位: {account['features']['current_seats']}\n"
        details += f"添加时间: {account.get('added_time', '未知')}\n"
        details += f"最后更新: {account['last_updated']}\n"
        details += f"备注: {account.get('notes', '无')}\n"

        self.detail_text.insert(tk.END, details)
        self.detail_text.config(state=tk.DISABLED)

    def update_switcher_status(self, message):
        """更新切换器状态"""
        self.status_var.set(message)
        self.root.update()

    def apply_to_vscode_safe(self):
        """安全切换账号 - 模拟正常登录流程，避免风控"""
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]

        # 检查是否有该账号的备份
        available_backups = self.vscode_manager.list_account_backups()

        if email not in available_backups:
            messagebox.showwarning("警告",
                f"未找到账号 {email} 的备份文件。\n\n"
                "请先手动登录该账号，然后点击'创建备份'按钮。")
            return

        # 确认切换
        if not messagebox.askyesno("确认安全切换",
                f"即将安全切换到账号: {email}\n\n"
                "安全切换会模拟正常的登出→登入流程，\n"
                "包括清理临时数据、更新时间戳等，\n"
                "以避免触发风控系统。\n\n"
                "整个过程需要约10-15秒，是否继续？"):
            return

        # 执行安全切换
        self.status_var.set(f"正在安全切换到账号: {email}...")

        def safe_switch_task():
            try:
                if self.vscode_manager.switch_to_account_safe(email):
                    self.status_var.set(f"安全切换成功: {email}")
                    messagebox.showinfo("成功",
                        f"已成功安全切换到账号: {email}\n\n"
                        "VSCode已重启，请等待Augment加载完成。\n"
                        "如果仍有问题，请等待几分钟后再使用。")
                else:
                    self.status_var.set("安全切换失败")
                    messagebox.showerror("失败", "安全切换失败，请查看日志")
            except Exception as e:
                self.status_var.set(f"安全切换失败: {str(e)}")
                messagebox.showerror("错误", f"安全切换失败: {str(e)}")

        # 在后台线程中执行切换
        threading.Thread(target=safe_switch_task, daemon=True).start()

    def apply_to_vscode_complete(self):
        """完全切换账号 - 基于备份恢复（100%可靠）"""
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]

        # 检查是否有该账号的备份
        available_backups = self.vscode_manager.list_account_backups()

        if email not in available_backups:
            messagebox.showwarning("警告",
                f"未找到账号 {email} 的备份文件。\n\n"
                "请先手动登录该账号，然后点击'创建备份'按钮。\n"
                "之后就可以使用'完全切换'功能了。")
            return

        # 确认切换
        if not messagebox.askyesno("确认切换",
                f"即将切换到账号: {email}\n\n"
                "这将关闭VSCode并恢复该账号的完整状态。\n"
                "是否继续？"):
            return

        # 执行切换
        self.status_var.set(f"正在切换到账号: {email}...")
        try:
            if self.vscode_manager.switch_to_account_simple(email):
                self.status_var.set(f"账号切换成功: {email}")
                messagebox.showinfo("成功", f"已成功切换到账号: {email}\nVSCode已重启")
            else:
                self.status_var.set("账号切换失败")
                messagebox.showerror("失败", "账号切换失败，请查看日志")
        except Exception as e:
            self.status_var.set(f"切换失败: {str(e)}")
            messagebox.showerror("错误", f"账号切换失败: {str(e)}")

    def create_account_backup(self):
        """为当前登录的账号创建备份"""
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]

        # 确认创建备份
        if not messagebox.askyesno("确认创建备份",
                f"即将为账号 {email} 创建完整的状态备份。\n\n"
                "请确保当前VSCode已登录该账号且工作正常。\n"
                "是否继续？"):
            return

        # 创建备份
        self.status_var.set(f"正在为账号 {email} 创建备份...")
        try:
            backup_path = self.vscode_manager.create_account_backup(email)
            if backup_path:
                self.status_var.set(f"备份创建成功: {email}")
                messagebox.showinfo("成功",
                    f"已为账号 {email} 创建备份！\n\n"
                    f"备份文件位置：\n{backup_path}\n\n"
                    "现在可以使用'完全切换'功能快速切换到该账号了。")
            else:
                self.status_var.set("备份创建失败")
                messagebox.showerror("失败", "备份创建失败，请查看日志")
        except Exception as e:
            self.status_var.set(f"备份失败: {str(e)}")
            messagebox.showerror("错误", f"备份创建失败: {str(e)}")

    def check_session_validity(self):
        """检测选中账号的session有效性"""
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)

        if not account:
            messagebox.showerror("错误", "未找到账号信息")
            return

        self.status_var.set(f"正在检测账号 {email} 的有效性...")

        def check_task():
            try:
                # 尝试获取账号详细信息
                api_data = self.manager._get_account_details(account["session"])

                # 检查是否有错误
                has_error = False
                error_messages = []

                for key, data in api_data.items():
                    if isinstance(data, dict) and "error" in data:
                        has_error = True
                        error_messages.append(f"{key}: {data['error']}")

                # 更新UI
                if has_error:
                    self.status_var.set(f"账号 {email} 的session已失效")
                    messagebox.showwarning("Session失效",
                        f"账号 {email} 的session已失效！\n\n"
                        f"错误信息：\n" + "\n".join(error_messages) + "\n\n"
                        "建议：\n"
                        "1. 手动重新登录该账号\n"
                        "2. 重新创建备份\n"
                        "3. 或者刷新该账号获取新的session")
                else:
                    # 解析有效期信息
                    sub_data = api_data.get("subscription", {})
                    billing_end = sub_data.get("billingPeriodEnd", "未知")
                    trial_end = sub_data.get("trialPeriodEnd", "未知")

                    self.status_var.set(f"账号 {email} 的session有效")
                    messagebox.showinfo("Session有效",
                        f"账号 {email} 的session当前有效！\n\n"
                        f"账单周期结束：{billing_end}\n"
                        f"试用结束：{trial_end}\n\n"
                        "备份文件应该可以正常使用。")

            except Exception as e:
                self.status_var.set(f"检测失败: {str(e)}")
                messagebox.showerror("检测失败", f"无法检测session有效性：{str(e)}")

        # 在后台线程中执行检测
        threading.Thread(target=check_task, daemon=True).start()

    def open_backup_directory(self):
        """打开备份文件目录"""
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            backup_dir = os.path.join(script_dir, "account_backups")

            # 如果目录不存在，先创建它
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir, exist_ok=True)
                messagebox.showinfo("目录创建", f"备份目录已创建：\n{backup_dir}")

            # 根据操作系统打开目录
            if os.name == 'nt':  # Windows
                os.startfile(backup_dir)
            elif os.name == 'posix':  # macOS and Linux
                if hasattr(os, 'uname') and os.uname().sysname == 'Darwin':  # macOS
                    subprocess.run(['open', backup_dir])
                else:  # Linux
                    subprocess.run(['xdg-open', backup_dir])

            self.status_var.set(f"已打开备份目录: {backup_dir}")

        except Exception as e:
            messagebox.showerror("错误", f"无法打开备份目录: {str(e)}")
            self.status_var.set(f"打开目录失败: {str(e)}")

    def browser_cookie_method(self):
        """浏览器Cookie注入法"""
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)

        if not account:
            messagebox.showerror("错误", "未找到账号信息")
            return

        self.browser_switcher.method1_browser_cookie_injection(account["session"], email)

    def manual_guided_method(self):
        """手动引导登录法"""
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)

        if not account:
            messagebox.showerror("错误", "未找到账号信息")
            return

        self.browser_switcher.method2_manual_guided_login(account["session"], email)

    def profile_isolation_method(self):
        """浏览器配置文件隔离法"""
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)

        if not account:
            messagebox.showerror("错误", "未找到账号信息")
            return

        self.browser_switcher.method3_profile_isolation(account["session"], email)

    def vm_isolation_method(self):
        """虚拟机隔离法"""
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)

        if not account:
            messagebox.showerror("错误", "未找到账号信息")
            return

        self.browser_switcher.method4_vm_isolation(account["session"], email)

    def apply_to_vscode_direct(self):
        """直接切换账号 - 操作数据库"""
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)

        if not account:
            self.status_var.set("应用账号失败")
            return

        # 检查 VSCode 是否运行
        if self.vscode_manager.is_vscode_running():
            if not messagebox.askyesno("确认",
                    "VSCode 正在运行，需要关闭才能更新会话。\n是否继续？"):
                return

            self.status_var.set("正在关闭 VSCode...")
            if not self.vscode_manager.close_vscode():
                messagebox.showerror("错误", "无法关闭 VSCode")
                return

        # 更新会话
        self.status_var.set("正在更新 VSCode 会话...")
        try:
            if self.vscode_manager.update_session(account["session"]):
                self.status_var.set("正在重启 VSCode...")
                self.vscode_manager.restart_vscode()
                self.status_var.set(f"账号 {email} 已成功应用到 VSCode")
                messagebox.showinfo("成功", "账号切换完成，VSCode 已重启")
            else:
                messagebox.showerror("错误", "更新会话失败，请查看日志")
                self.status_var.set("更新会话失败")
        except Exception as e:
            messagebox.showerror("错误", f"应用账号失败: {str(e)}")
            self.status_var.set(f"应用失败: {str(e)}")

    def apply_to_vscode_auto(self):
        """自动切换账号 - 使用自动化流程"""
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)

        if account:
            # 检查是否存储了密码
            if email not in self.account_credentials:
                self.prompt_for_credentials(email)
                return

            # 获取凭证
            password = self.account_credentials[email].get('password', '')

            if not password:
                messagebox.showwarning("警告", "该账号未设置密码")
                return

            # 启动切换流程
            self.status_var.set(f"开始自动切换到账号: {email}")
            self.switcher.start_login_process(
                account["session"],
                email,
                password
            )
        else:
            self.status_var.set("应用账号失败")

    def prompt_for_credentials(self, email):
        """提示输入账号凭证"""
        dialog = tk.Toplevel(self.root)
        dialog.title("输入账号凭证")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text=f"为 {email} 输入密码:").pack(pady=10)

        password_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=password_var, show="*", width=30).pack(pady=5)

        save_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(dialog, text="保存密码", variable=save_var).pack(pady=5)

        def on_submit():
            password = password_var.get()
            if not password:
                messagebox.showerror("错误", "密码不能为空")
                return

            if save_var.get():
                self.account_credentials[email] = {'password': password}

            dialog.destroy()
            # 重新尝试应用
            self.apply_to_vscode_auto()

        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(pady=10)

        ttk.Button(btn_frame, text="确定", command=on_submit).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=10)

    def add_account(self):
        # 创建添加账号对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("添加新账号")
        dialog.geometry("500x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # 创建表单
        form_frame = ttk.Frame(dialog, padding=10)
        form_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(form_frame, text="Session 值:").grid(row=0, column=0, sticky=tk.W, pady=5)
        session_entry = scrolledtext.ScrolledText(form_frame, height=5, width=50)
        session_entry.grid(row=0, column=1, sticky=tk.W+tk.E, pady=5, padx=(0, 10))

        ttk.Label(form_frame, text="备注:").grid(row=1, column=0, sticky=tk.W, pady=5)
        notes_entry = ttk.Entry(form_frame, width=50)
        notes_entry.grid(row=1, column=1, sticky=tk.W+tk.E, pady=5, padx=(0, 10))

        # 按钮框架
        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(fill=tk.X, padx=10, pady=10)

        def on_submit():
            session = session_entry.get("1.0", tk.END).strip()
            notes = notes_entry.get().strip()

            if not session:
                messagebox.showerror("错误", "Session 值不能为空")
                return

            try:
                self.status_var.set("正在添加账号...")
                account = self.manager.add_account(session, notes)
                if account:
                    self.refresh_account_list()
                    dialog.destroy()
                    self.status_var.set(f"账号添加成功: {account['email']}")
                else:
                    self.status_var.set("账号添加失败")
            except Exception as e:
                messagebox.showerror("错误", f"添加账号失败: {str(e)}")
                self.status_var.set(f"添加账号失败: {str(e)}")

        ttk.Button(btn_frame, text="添加", command=on_submit).pack(side=tk.RIGHT, padx=5)
        ttk.Button(btn_frame, text="取消", command=dialog.destroy).pack(side=tk.RIGHT, padx=5)

    def refresh_selected_account(self):
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]

        self.status_var.set(f"正在刷新账号: {email}...")
        self.root.update()

        try:
            # 在后台线程中刷新账号
            def refresh_task():
                account = self.manager.refresh_account(email)
                if account:
                    self.refresh_account_list()
                    self.show_account_details(account)
                    self.status_var.set(f"账号刷新成功: {email}")
                else:
                    self.status_var.set(f"账号刷新失败: {email}")

            threading.Thread(target=refresh_task, daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"刷新账号失败: {str(e)}")
            self.status_var.set(f"刷新失败: {str(e)}")

    def refresh_all_accounts(self):
        if not self.manager.get_all_accounts():
            messagebox.showinfo("提示", "没有可刷新的账号")
            return

        self.status_var.set("正在刷新所有账号...")
        self.root.update()

        try:
            # 在后台线程中刷新所有账号
            def refresh_task():
                self.manager.refresh_all_accounts()
                self.refresh_account_list()
                self.status_var.set("所有账号刷新完成")

            threading.Thread(target=refresh_task, daemon=True).start()
        except Exception as e:
            messagebox.showerror("错误", f"刷新账号失败: {str(e)}")
            self.status_var.set(f"刷新失败: {str(e)}")

    def delete_account(self):
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]

        if messagebox.askyesno("确认", f"确定要删除账号 {email} 吗？"):
            if self.manager.remove_account(email):
                self.refresh_account_list()
                self.detail_text.config(state=tk.NORMAL)
                self.detail_text.delete(1.0, tk.END)
                self.detail_text.config(state=tk.DISABLED)
                self.status_var.set(f"已删除账号: {email}")
            else:
                self.status_var.set(f"删除账号失败: {email}")

    def copy_session(self):
        selected = self.account_tree.selection()
        if not selected:
            messagebox.showinfo("提示", "请先选择一个账号")
            return

        item = self.account_tree.item(selected[0])
        email = item["values"][1]
        account = self.manager.get_account(email)

        if account:
            pyperclip.copy(account["session"])
            self.status_var.set(f"已复制 {email} 的 Session 到剪贴板")
        else:
            self.status_var.set("复制 Session 失败")

    def export_accounts(self):
        filename = filedialog.asksaveasfilename(
            title="导出账号",
            filetypes=[("JSON 文件", "*.json")],
            defaultextension=".json",
            initialvalue="augment_accounts_backup.json"
        )

        if filename:
            if self.manager.export_accounts(filename):
                messagebox.showinfo("导出成功", f"账号已成功导出到文件: {filename}")
            else:
                messagebox.showerror("导出失败", "导出账号时发生错误")

    def import_accounts(self):
        filename = filedialog.askopenfilename(
            title="导入账号",
            filetypes=[("JSON 文件", "*.json")],
            defaultextension=".json"
        )

        if filename:
            count = self.manager.import_accounts(filename)
            if count > 0:
                self.refresh_account_list()
                messagebox.showinfo("导入成功", f"成功导入 {count} 个账号")
            else:
                messagebox.showinfo("导入完成", "没有新账号可导入")

    def start_auto_refresh(self):
        def auto_refresh_task():
            while True:
                if self.auto_refresh_var.get():
                    try:
                        # 每5分钟自动刷新一次
                        time.sleep(self.auto_refresh_interval)
                        if self.manager.get_all_accounts():
                            self.status_var.set("正在自动刷新账号...")
                            self.manager.refresh_all_accounts()
                            self.refresh_account_list()
                            self.status_var.set(f"自动刷新完成 ({datetime.now().strftime('%H:%M:%S')})")
                    except Exception as e:
                        logger.error(f"自动刷新失败: {str(e)}")
                else:
                    time.sleep(1)

        threading.Thread(target=auto_refresh_task, daemon=True).start()

    def toggle_auto_refresh(self):
        if self.auto_refresh_var.get():
            self.status_var.set("自动刷新已启用")
        else:
            self.status_var.set("自动刷新已禁用")

def main():
    """主函数"""
    root = tk.Tk()
    app = AugmentManagerApp(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap("icon.ico")
    except:
        pass

    # 启动应用
    root.mainloop()

if __name__ == "__main__":
    print("Augment 账号管理器 - 防风控版本")
    print("=" * 60)
    print("🛡️ 4种100%安全的切换方法，绝不封号：")
    print("1. Cookie注入：通过浏览器开发者工具手动注入")
    print("2. 手动引导：完全模拟人工登录流程")
    print("3. 配置隔离：每个账号使用独立浏览器配置")
    print("4. 虚拟机隔离：每个账号使用独立虚拟机（终极方案）")
    print("=" * 60)
    print("📋 使用流程：")
    print("1. 添加账号信息到管理器")
    print("2. 选择账号，点击任意一种安全切换方法")
    print("3. 按照提示完成切换操作")
    print("=" * 60)
    print("⚠️ 重要说明：")
    print("• 已移除数据库替换方法（容易封号）")
    print("• 所有方法都是100%安全，不会触发风控")
    print("• 推荐使用Cookie注入或配置隔离方法")
    print("• 虚拟机隔离适合管理大量账号")
    print("=" * 60)

    # 检查依赖
    missing_deps = []
    if not WINDOWS_AVAILABLE and os.name == 'nt':
        missing_deps.append("win32crypt, psutil (Windows专用)")
    if not CRYPTO_AVAILABLE:
        missing_deps.append("cryptography, pycryptodome (可选)")
    if not AUTOMATION_AVAILABLE:
        missing_deps.append("pyautogui, pynput, pytesseract, opencv-python, numpy (自动切换功能)")

    if missing_deps:
        print("缺少可选依赖库：")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("基础功能仍可正常使用")
        print("=" * 50)

    main()
